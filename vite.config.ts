import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import dts from "vite-plugin-dts";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    dts({
      include: ["src/components/ksgMap/**/*.{vue,ts}"],
    }),

    // compression({
    //   verbose: true,
    //   disable: false,
    //   threshold: 10240,
    //   algorithm: "gzip",
    //   ext: ".gz",
    // }),
  ],
  server: {
    proxy: {
      "/api": {
        // target: "http://**************:9200/", // 测试环境调试
        target: "http://**************:8001", // 测试环境调试

        // target: " http://ceh2if.natappfree.cc/", // 测试环境调试
        // target: "http://frps-hk.vxtrans.com:15049/",
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      "/local": {
        target: "http://127.0.0.1:3000/",
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/local/, ""),
      },
    },

    allowedHosts: [
      "frps-hk.vxtrans.com",
      "xxx.endlessorigin.com",
      "test.endlessorigin.com",
    ],
  },
  build: {
    outDir: "package", //打包后生成的文件名也等同于你的发布的组件名
    lib: {
      entry: path.resolve(__dirname, "./src/components/ksgMap/index.ts"), //指定组件编译入口文件
      name: "KsgMap",
      fileName: "ksgMap",
    }, //库编译模式配置
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: ["vue"],
      output: {
        assetFileNames: "css/[name].[ext]",
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: "Vue",
        },
      },
    },
  },
  esbuild: {
    keepNames: true,
  },
});
