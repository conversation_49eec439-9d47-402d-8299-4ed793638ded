const express = require("express");
const cors = require("cors");
const {
  generateMockData,
  generateMultipleRootMockData,
} = require("./data/mockDataGenerator.js");

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  console.log("请求体:", JSON.stringify(req.body, null, 2));
  next();
});

// 单根节点知识图谱API
app.post("/champaign/klg/graph", (req, res) => {
  const { klgCodes, current = 1, limit = 10 } = req.body;

  console.log("单根节点请求参数:", { klgCodes, current, limit });
  const klgCode = klgCodes[0];
  const mockData = generateMockData(klgCode, current, limit);

  res.json({
    code: 200,
    message: "成功",
    data: mockData,
  });
});

// 多根节点知识图谱API
app.post("/champaign/klg/prjGraph", (req, res) => {
  const { klgCodes, current = 1, limit = 10, spuId } = req.body;

  console.log("多根节点请求参数:", { klgCodes, current, limit, spuId });

  // 兼容两种参数格式
  const rootId = klgCodes ? klgCodes[0] : spuId || "1";
  const mockData = generateMultipleRootMockData(rootId, current, limit);

  res.json({
    code: 200,
    message: "成功",
    data: mockData,
  });
});
