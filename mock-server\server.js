const express = require('express');
const cors = require('cors');
const { generateMockData, generateMultipleRootMockData } = require('./data/mockDataGenerator.js');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  console.log('请求体:', JSON.stringify(req.body, null, 2));
  next();
});

// 单根节点知识图谱API
app.post('/champaign/klg/graph', (req, res) => {
  try {
    const { klgCodes, current = 1, limit = 10 } = req.body;
    
    console.log('单根节点请求参数:', { klgCodes, current, limit });
    
    if (!klgCodes || !Array.isArray(klgCodes) || klgCodes.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '参数错误：klgCodes必须是非空数组',
        data: null
      });
    }

    const klgCode = klgCodes[0];
    const mockData = generateMockData(klgCode, current, limit);
    
    res.json({
      code: 200,
      message: '成功',
      data: mockData
    });
  } catch (error) {
    console.error('单根节点API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

// 多根节点知识图谱API
app.post('/champaign/klg/prjGraph', (req, res) => {
  try {
    const { klgCodes, current = 1, limit = 10, spuId } = req.body;
    
    console.log('多根节点请求参数:', { klgCodes, current, limit, spuId });
    
    // 兼容两种参数格式
    const rootId = klgCodes ? klgCodes[0] : spuId || '1';
    const mockData = generateMultipleRootMockData(rootId, current, limit);
    
    res.json({
      code: 200,
      message: '成功',
      data: mockData
    });
  } catch (error) {
    console.error('多根节点API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'ksg-map-mock-server'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: `接口不存在: ${req.method} ${req.originalUrl}`,
    data: null
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: null
  });
});

app.listen(PORT, () => {
  console.log(`🚀 KSG知识图谱模拟服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 单根节点API: POST /champaign/klg/graph`);
  console.log(`📊 多根节点API: POST /champaign/klg/prjGraph`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});
