attribute float size;
attribute vec3 customColor;
attribute float customOpacity;
attribute float customRandom;
attribute float breathStatus; //节点是否呼吸0-否 1-是
varying vec3 vColor;
varying float vOpacity;
varying vec2 vUv;
varying float vCustomRandom;
varying float vIsBreathAni;


void main() {
    vUv = uv;
    vColor = customColor;
    vOpacity = customOpacity;
    vCustomRandom = customRandom;
    vIsBreathAni = breathStatus;
    vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
    gl_PointSize = 10.0 * ( 350.0 / -mvPosition.z );
    gl_Position = projectionMatrix * mvPosition;
}