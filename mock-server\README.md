# KSG知识图谱模拟服务器

这是一个为KSG知识图谱前端应用提供模拟数据的Node.js后端服务。

## 功能特性

- 🎯 **单根节点模式**：模拟单个根知识点的层级结构
- 🌐 **多根节点模式**：模拟多个根知识点的复杂图谱
- 📊 **分页支持**：支持分页加载大量数据
- 🔗 **关系生成**：自动生成知识点之间的连接关系
- 🎨 **随机数据**：生成丰富的模拟数据用于测试
- 📝 **请求日志**：详细的请求和响应日志

## 快速开始

### 1. 安装依赖

```bash
cd mock-server
npm install
```

### 2. 启动服务器

```bash
# 生产模式
npm start

# 开发模式（自动重启）
npm run dev
```

服务器将在 `http://localhost:3000` 启动。

### 3. 验证服务

访问健康检查接口：
```bash
curl http://localhost:3000/health
```

## API接口

### 单根节点知识图谱

**POST** `/champaign/klg/graph`

请求体：
```json
{
  "klgCodes": ["K123456"],
  "current": 1,
  "limit": 10
}
```

响应：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "dataList": [...],
    "connections": [...],
    "total": 50,
    "current": 1,
    "limit": 10,
    "hasMore": true
  }
}
```

### 多根节点知识图谱

**POST** `/champaign/klg/prjGraph`

请求体：
```json
{
  "klgCodes": ["1"],
  "current": 1,
  "limit": 10
}
```

或者：
```json
{
  "spuId": "project123",
  "current": 1,
  "limit": 10
}
```

## 数据结构

### 知识点节点
```json
{
  "id": "K1704649123456",
  "name": "数学基础概念",
  "level": 1,
  "parentId": null,
  "color": "#FF6B6B",
  "position": {
    "x": 10.5,
    "y": -20.3,
    "z": 15.7
  },
  "metadata": {
    "difficulty": 3,
    "importance": 4,
    "studyTime": 90,
    "tags": "核心"
  }
}
```

### 连接关系
```json
{
  "id": "conn_K123_K456",
  "source": "K123",
  "target": "K456",
  "type": "prerequisite",
  "strength": 0.8
}
```

## 配置说明

- **端口**：默认3000，可通过环境变量 `PORT` 修改
- **CORS**：已启用跨域支持
- **日志**：自动记录所有请求和响应

## 与前端集成

确保前端Vite配置中的代理设置正确：

```javascript
// vite.config.ts
server: {
  proxy: {
    "/local": {
      target: "http://127.0.0.1:3000/",
      secure: false,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/local/, ""),
    },
  },
}
```

## 开发说明

- 数据生成逻辑在 `data/mockDataGenerator.js`
- 支持自定义数据结构和生成规则
- 可根据实际需求调整模拟数据的复杂度和数量
