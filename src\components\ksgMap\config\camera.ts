import { PerspectiveCamera } from "three";
import ctx from "../ctx";
import type { CameraConfig } from "../types/index";
/**
 *配置相机
 */
export default function useCamera(
  config: CameraConfig = {
    fov: 45,
    // aspect: 1.778, //在初始化的时候需要根据画布尺寸配置，默认900：506
    aspect: 1200 / 675, //在初始化的时候需要根据画布尺寸配置，默认900：506
    near: 0.1,
    far: 1000,
    position: {
      x: 30.2,
      y: -3.14,
      z: 24.98,
    },
  }
) {
  const camera = new PerspectiveCamera(
    config.fov,
    config.aspect,
    config.near,
    config.far
  );
  ctx.camera = camera;
  camera.position.set(
    config.position?.x!,
    config.position?.y!,
    config.position?.z!
  );
  return {
    camera,
  };
}
