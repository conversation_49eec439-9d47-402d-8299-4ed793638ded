# KsgMap 部署与使用指南

## 🚀 启动方式

### 开发环境启动

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 访问应用
# 浏览器打开: http://localhost:5173
```

### 生产环境构建

```bash
# 构建组件库
npm run build

# 构建产物位置
# - package/ksgMap.js (UMD格式)
# - package/ksgMap.umd.cjs (CommonJS格式)
# - types/ (TypeScript类型定义)
```

## 📦 安装和集成

### 1. NPM包安装

```bash
# 从NPM安装
npm install @endlessorigin/KsgMap

# 或从本地构建产物安装
npm install ./package
```

### 2. 项目集成

#### Vue 3项目集成

```typescript
// main.ts
import { createApp } from 'vue'
import KsgMapGlobal from '@endlessorigin/KsgMap'
import App from './App.vue'

const app = createApp(App)
app.use(KsgMapGlobal)
app.mount('#app')
```

#### 按需导入

```vue
<script setup lang="ts">
import { KsgMap, MODE, dataLoader } from '@endlessorigin/KsgMap'

// 使用dataLoader辅助函数
const { loading, init, loadMore } = dataLoader({
  api: fetchKnowledgeData,
  rootId: 'root-node-id'
})
</script>
```

### 3. CDN方式使用

```html
<!DOCTYPE html>
<html>
<head>
  <title>KsgMap CDN示例</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://unpkg.com/@endlessorigin/KsgMap/package/ksgMap.umd.cjs"></script>
</head>
<body>
  <div id="app">
    <ksg-map :config="config" :loading="loading"></ksg-map>
  </div>
  
  <script>
    const { createApp } = Vue
    const { KsgMap, MODE } = KsgMapGlobal
    
    createApp({
      components: { KsgMap },
      data() {
        return {
          loading: 'loaded',
          config: {
            model: MODE.Single_ROOT
          }
        }
      }
    }).mount('#app')
  </script>
</body>
</html>
```

## 🔧 配置说明

### 环境配置

#### Vite配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  
  // 开发服务器配置
  server: {
    host: '0.0.0.0',  // 允许外部访问
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  }
})
```

#### TypeScript配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### API接口配置

#### 数据接口示例

```typescript
// api.ts
export interface KnowledgeAPI {
  // 获取知识点数据
  getKnowledgePoints(params: {
    current: number
    levelSize: number
    rootId?: string
  }): Promise<{
    code: string
    data: {
      dataList: PointData[]
      total: number
    }
    message: string
    success: boolean
  }>
}

// 实现示例
export const knowledgeAPI: KnowledgeAPI = {
  async getKnowledgePoints(params) {
    const response = await fetch('/api/knowledge/points', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    })
    
    return response.json()
  }
}
```

#### 数据加载器配置

```typescript
// 使用内置dataLoader
import { dataLoader } from '@endlessorigin/KsgMap'

const { loading, data, init, loadMore } = dataLoader({
  api: knowledgeAPI.getKnowledgePoints,
  rootId: 'math-basic',
  config: {
    current: 1,
    levelSize: 3
  }
})

// 在组件中使用
onMounted(() => {
  init()
})
```

## 🌐 部署方案

### 1. 静态部署

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/ksg-map;
    index index.html;
    
    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend-server:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  ksg-map:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
```

### 2. 云平台部署

#### Vercel部署

```json
// vercel.json
{
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "https://your-api-server.com/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

#### Netlify部署

```toml
# netlify.toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/api/*"
  to = "https://your-api-server.com/api/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## 🔍 监控和调试

### 性能监控

```typescript
// 性能监控配置
import Stats from 'three/examples/jsm/libs/stats.module.js'

// 开发环境启用性能监控
if (import.meta.env.DEV) {
  const stats = new Stats()
  stats.showPanel(0) // 0: fps, 1: ms, 2: mb
  document.body.appendChild(stats.dom)
  
  function animate() {
    stats.begin()
    // 渲染逻辑
    stats.end()
    requestAnimationFrame(animate)
  }
  animate()
}
```

### 错误监控

```typescript
// 错误边界处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err)
  console.error('组件信息:', info)
  
  // 发送错误报告到监控服务
  if (import.meta.env.PROD) {
    sendErrorReport({
      error: err.message,
      stack: err.stack,
      component: info
    })
  }
}

// Three.js错误处理
window.addEventListener('error', (event) => {
  if (event.filename?.includes('three')) {
    console.error('Three.js错误:', event.error)
  }
})
```

### 调试工具

```typescript
// 开发环境调试工具
if (import.meta.env.DEV) {
  // 暴露全局调试对象
  window.__KSG_DEBUG__ = {
    ctx: ctx,
    scene: ctx.scene,
    camera: ctx.camera,
    renderer: ctx.renderer,
    graph: ctx.graph
  }
  
  // 调试快捷键
  window.addEventListener('keydown', (event) => {
    if (event.ctrlKey && event.key === 'd') {
      console.log('KsgMap调试信息:', window.__KSG_DEBUG__)
    }
  })
}
```

## 📊 性能优化

### 构建优化

```typescript
// vite.config.ts 优化配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'three': ['three'],
          'vue': ['vue'],
          'vendor': ['lodash', '@tweenjs/tween.js']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

### 运行时优化

```typescript
// 懒加载大型资源
const loadHeavyResources = async () => {
  const [textureLoader, modelLoader] = await Promise.all([
    import('three/examples/jsm/loaders/TextureLoader.js'),
    import('three/examples/jsm/loaders/GLTFLoader.js')
  ])
  
  return { textureLoader, modelLoader }
}

// 使用Web Workers处理复杂计算
const worker = new Worker('/workers/graph-calculation.js')
worker.postMessage({ pointsData })
worker.onmessage = (event) => {
  const { calculatedGraph } = event.data
  // 使用计算结果
}
```

## 🔒 安全配置

### CSP配置

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-eval'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:;">
```

### HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 强制HTTPS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
}
```

---

通过以上配置，你可以成功部署和使用KsgMap组件。如有问题，请参考[故障排除指南](README.md#-故障排除)或提交Issue。
