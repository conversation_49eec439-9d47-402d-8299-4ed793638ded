# KsgMap 快速开始指南

## 🚀 5分钟快速上手

### 第一步：安装依赖

```bash
# 使用npm
npm install @endlessorigin/KsgMap

# 使用yarn
yarn add @endlessorigin/KsgMap

# 使用pnpm
pnpm add @endlessorigin/KsgMap
```

### 第二步：基础使用

创建一个简单的知识图谱：

```vue
<template>
  <div style="width: 100%; height: 600px;">
    <KsgMap
      ref="mapRef"
      :loading="loading"
      @click-label="handleNodeClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { KsgMap } from '@endlessorigin/KsgMap'

const mapRef = ref()
const loading = ref<'loading' | 'loaded' | 'error'>('loading')

// 模拟知识点数据
const mockData = [
  {
    id: '1',
    pointName: '数学基础',
    parentIds: [],
    isMilestone: true,
    status: 1
  },
  {
    id: '2',
    pointName: '代数',
    parentIds: ['1'],
    isMilestone: false,
    status: 1
  },
  {
    id: '3',
    pointName: '几何',
    parentIds: ['1'],
    isMilestone: false,
    status: 0
  },
  {
    id: '4',
    pointName: '线性代数',
    parentIds: ['2'],
    isMilestone: false,
    status: 0
  }
]

onMounted(async () => {
  loading.value = 'loading'
  
  // 加载数据
  await mapRef.value?.firstLoadPointsData(mockData, mockData.length, '1')
  
  loading.value = 'loaded'
})

function handleNodeClick(nodeId: string) {
  console.log('点击了节点:', nodeId)
}
</script>
```

### 第三步：运行项目

```bash
npm run dev
```

恭喜！🎉 你已经成功创建了第一个3D知识图谱！

## 🎛️ 常用配置

### 单根节点模式（推荐新手）

```vue
<template>
  <KsgMap
    :config="singleRootConfig"
    :loading="loading"
    @load-more="handleLoadMore"
  />
</template>

<script setup lang="ts">
import { MODE } from '@endlessorigin/KsgMap'

const singleRootConfig = {
  model: MODE.Single_ROOT,
  pointsLevelPager: {
    current: 1,
    levelSize: 3  // 每次加载3层
  }
}

async function handleLoadMore(rootId: string, current: number, levelSize: number) {
  // 加载更多数据的逻辑
  console.log(`加载更多: rootId=${rootId}, current=${current}, levelSize=${levelSize}`)
}
</script>
```

### 多根节点模式

```vue
<script setup lang="ts">
const multiRootConfig = {
  model: MODE.MULTIPLE_ROOT,
  scene: {
    backgroundIntensity: 0.03,
    groupPosition: [0, 5, 0]
  },
  point: {
    radius: 0.6,
    space: 2.5
  }
}
</script>
```

### 自定义样式配置

```vue
<script setup lang="ts">
const customConfig = {
  // 相机设置
  camera: {
    fov: 50,
    position: { x: 25, y: -5, z: 20 }
  },
  
  // 连线流光效果
  line: {
    length: 0.6,
    speed: 0.25,
    isRandom: true
  },
  
  // 悬停标签偏移
  hoverLabel: {
    offsetX: 20,
    offsetY: -10
  }
}
</script>
```

## 📊 数据格式说明

### 基础数据结构

```typescript
interface PointData {
  id: string              // 唯一标识符
  pointName: string       // 显示名称
  parentIds: string[]     // 父节点ID数组
  isMilestone: boolean    // 是否为里程碑节点
  status: number          // 学习状态：0=未学习，1=已学习
}
```

### 数据示例

```javascript
const knowledgeData = [
  // 根节点
  {
    id: 'root',
    pointName: 'JavaScript基础',
    parentIds: [],
    isMilestone: true,
    status: 1
  },
  
  // 第一层子节点
  {
    id: 'variables',
    pointName: '变量与数据类型',
    parentIds: ['root'],
    isMilestone: false,
    status: 1
  },
  {
    id: 'functions',
    pointName: '函数',
    parentIds: ['root'],
    isMilestone: false,
    status: 0
  },
  
  // 第二层子节点
  {
    id: 'arrow-functions',
    pointName: '箭头函数',
    parentIds: ['functions'],
    isMilestone: false,
    status: 0
  },
  {
    id: 'closures',
    pointName: '闭包',
    parentIds: ['functions'],
    isMilestone: true,
    status: 0
  }
]
```

## 🎮 交互功能

### 内置交互

- **鼠标拖拽**：旋转视角
- **滚轮缩放**：放大缩小场景
- **节点点击**：触发 `clickLabel` 事件
- **节点悬停**：显示节点信息
- **双击**：进入全局视图
- **工具栏按钮**：全屏、回退、回到根节点

### 自定义交互

```vue
<template>
  <KsgMap
    @click-label="handleNodeClick"
    @load-more="handleLoadMore"
  />
</template>

<script setup lang="ts">
function handleNodeClick(nodeId: string) {
  // 处理节点点击
  switch(nodeId) {
    case 'math-basic':
      // 跳转到数学基础详情页
      router.push(`/course/${nodeId}`)
      break
    case 'programming':
      // 显示编程相关信息
      showProgrammingInfo()
      break
    default:
      console.log('点击节点:', nodeId)
  }
}

function handleLoadMore(rootId: string, current: number, levelSize: number) {
  // 异步加载更多数据
  fetchMoreKnowledgePoints(rootId, current, levelSize)
    .then(data => {
      mapRef.value?.loadMorePointsData(data)
    })
}
</script>
```

## 🔧 开发环境设置

### 本地开发

```bash
# 克隆项目
git clone https://github.com/your-repo/ksg-map.git
cd ksg-map

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建组件库
npm run build
```

### 项目结构

```
your-project/
├── src/
│   ├── components/
│   │   └── KnowledgeMap.vue    # 使用KsgMap的组件
│   ├── data/
│   │   └── knowledge.json      # 知识点数据
│   └── App.vue
├── package.json
└── vite.config.ts
```

### Vite配置示例

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 🐛 常见问题解决

### 问题1：组件不显示

**症状**：页面空白，控制台无错误

**解决方案**：
```vue
<template>
  <!-- 确保容器有明确的尺寸 -->
  <div style="width: 100%; height: 600px; border: 1px solid #ccc;">
    <KsgMap :loading="loading" />
  </div>
</template>
```

### 问题2：数据加载失败

**症状**：loading状态一直是'loading'

**解决方案**：
```javascript
// 检查数据格式
const validData = data.map(item => ({
  id: String(item.id),           // 确保id是字符串
  pointName: item.name || '',    // 确保有名称
  parentIds: Array.isArray(item.parentIds) ? item.parentIds : [], // 确保是数组
  isMilestone: Boolean(item.isMilestone),
  status: Number(item.status) || 0
}))
```

### 问题3：性能问题

**症状**：大量节点时卡顿

**解决方案**：
```javascript
const optimizedConfig = {
  pointsLevelPager: {
    current: 1,
    levelSize: 2  // 减少每次加载的层数
  },
  line: {
    isRandom: false  // 关闭随机流光效果
  }
}
```

## 📚 下一步

- 查看 [完整API文档](README.md#-api接口)
- 了解 [技术实现原理](TECHNICAL_GUIDE.md)
- 参考 [更多示例](examples/)
- 加入 [社区讨论](https://github.com/your-repo/discussions)

---

🎉 现在你已经掌握了KsgMap的基础使用！开始构建你的3D知识图谱吧！
