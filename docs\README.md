# KsgMap - 3D知识图谱可视化组件

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/EndlessOrigin/ksg-map)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Vue](https://img.shields.io/badge/vue-3.5.13-brightgreen.svg)](https://vuejs.org/)
[![Three.js](https://img.shields.io/badge/three.js-0.175.0-orange.svg)](https://threejs.org/)

一个集成了单个领域下单个根知识点和多个根知识点可视化的3D图谱组件，基于Vue 3和Three.js构建。

## ✨ 特性

- 🎯 **双模式支持**：单根节点模式和多根节点模式
- 🎨 **3D可视化**：基于Three.js的流畅3D渲染
- 🔄 **动态加载**：支持分层懒加载，优化大数据集性能
- 🎮 **丰富交互**：节点点击、悬停、拖拽、缩放等交互功能
- ✨ **流光效果**：连线流光动画，增强视觉效果
- 📱 **响应式设计**：自适应容器尺寸变化
- 🎛️ **高度可配置**：丰富的配置选项，满足不同场景需求
- 🔧 **TypeScript支持**：完整的类型定义

## 🏗️ 技术架构

### 核心架构图

```mermaid
graph TB
    A[KsgMap Vue组件] --> B[配置管理层]
    A --> C[数据处理层]
    A --> D[渲染引擎层]
    A --> E[交互控制层]
    
    B --> B1[useInitThreeJsConfig<br/>配置初始化]
    B --> B2[默认配置<br/>相机/渲染器/场景]
    
    C --> C1[KsgGraph<br/>DAG图计算]
    C --> C2[数据加载器<br/>dataLoader]
    C --> C3[层级计算<br/>坐标计算]
    
    D --> D1[Three.js场景<br/>Scene/Camera/Renderer]
    D --> D2[CSS2D渲染器<br/>标签渲染]
    D --> D3[动画系统<br/>Tween.js]
    
    E --> E1[事件处理<br/>点击/悬停/拖拽]
    E --> E2[相机控制<br/>OrbitControls]
    E --> E3[视图切换<br/>聚焦/回退]
```

### 数据处理流程

```mermaid
flowchart TD
    A[原始知识点数据<br/>PointData[]] --> B[KsgGraph构造函数]
    B --> C[build方法<br/>构建节点关系]
    C --> D[computeLevel方法<br/>计算层级结构]
    D --> E[computePointPosition方法<br/>计算3D坐标]
    
    C --> C1[父子关系转换]
    C --> C2[节点Map构建]
    
    D --> D1[BFS层级遍历]
    D --> D2[idLevelMap构建]
    
    E --> E1[层级间距计算]
    E --> E2[节点间距计算]
    E --> E3[3D坐标生成]
    
    E3 --> F[渲染数据准备完成]
    F --> G[渲染函数调用]
    G --> H[Three.js场景渲染]
```

## 🔧 实现原理

### 1. 数据结构处理

**KsgGraph类**是核心的数据处理引擎，负责将原始的知识点数据转换为可渲染的3D图结构：

- **DAG构建**：将知识点的父子关系转换为有向无环图(DAG)结构
- **层级计算**：使用BFS算法计算每个节点的层级深度
- **坐标计算**：根据层级和节点间距计算每个节点的3D坐标位置

### 2. 渲染引擎

基于Three.js构建的3D渲染系统：

- **场景管理**：Scene、Camera、Renderer的统一管理
- **几何体渲染**：节点使用球体几何体，连线使用管道几何体
- **材质系统**：支持不同状态的节点材质（已学习、未学习、里程碑等）
- **CSS2D渲染**：节点标签使用CSS2DRenderer实现

### 3. 交互控制

完整的用户交互体系：

- **相机控制**：基于OrbitControls的3D场景导航
- **事件处理**：鼠标点击、悬停、拖拽等事件的统一处理
- **视图切换**：支持聚焦到特定节点、回退到上级视图等功能

### 4. 动画系统

流畅的动画效果：

- **节点动画**：使用Tween.js实现节点的出现和移动动画
- **流光效果**：连线上的流光动画，支持速度和随机性配置
- **相机动画**：视图切换时的平滑相机过渡

## 📦 安装

```bash
npm install @endlessorigin/KsgMap
```

## 🚀 快速开始

### 1. 全局注册

```typescript
import { createApp } from 'vue'
import KsgMapGlobal from '@endlessorigin/KsgMap'
import App from './App.vue'

const app = createApp(App)
app.use(KsgMapGlobal)
app.mount('#app')
```

### 2. 按需导入

```vue
<template>
  <div class="container">
    <KsgMap
      ref="ksgRef"
      :config="config"
      :loading="loading"
      @load-more="handleLoadMore"
      @click-label="handleClickLabel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { KsgMap, MODE, type Options } from '@endlessorigin/KsgMap'

const ksgRef = ref()
const loading = ref<'loading' | 'loaded' | 'error'>('loading')

// 配置选项
const config: Options = {
  model: MODE.Single_ROOT, // 单根节点模式
  pointsLevelPager: {
    current: 1,
    levelSize: 4
  }
}

// 初始化数据加载
onMounted(async () => {
  loading.value = 'loading'
  try {
    const data = await fetchKnowledgeData()
    await ksgRef.value?.firstLoadPointsData(data.dataList, data.total, data.rootId)
    loading.value = 'loaded'
  } catch (error) {
    loading.value = 'error'
  }
})

// 加载更多数据
async function handleLoadMore(rootId: string, current: number, levelSize: number) {
  loading.value = 'loading'
  const data = await fetchMoreData(rootId, current, levelSize)
  await ksgRef.value?.loadMorePointsData(data.dataList)
  loading.value = 'loaded'
}

// 节点点击处理
function handleClickLabel(id: string) {
  console.log('点击节点:', id)
}
</script>
```

## 🎛️ 配置选项

### 基础配置

```typescript
interface Options {
  // 渲染模式
  model?: MODE.Single_ROOT | MODE.MULTIPLE_ROOT
  
  // 组件尺寸
  width?: number | string  // 默认: 1200
  height?: number | string // 默认: 675
  
  // 分层加载配置
  pointsLevelPager?: {
    current: number    // 当前层级
    levelSize: number  // 每次加载层数
    total?: number     // 总节点数
  }
  
  // 层级间距
  levelSpace?: number // 默认: 15
  
  // 节点配置
  point?: {
    radius: number // 节点半径，默认: 0.5
    space: number  // 节点间距，默认: 2
  }
  
  // 连线流光效果
  line?: {
    length?: number    // 流光长度，默认: 0.4
    speed?: number     // 流光速度，默认: 0.15
    isRandom?: boolean // 是否随机，默认: false
  }
}
```

### 高级配置

```typescript
interface Options {
  // 相机配置
  camera?: {
    fov: number        // 视野角度
    aspect: number     // 宽高比
    near: number       // 近裁剪面
    far: number        // 远裁剪面
    position: {        // 相机位置
      x: number
      y: number
      z: number
    }
  }
  
  // 渲染器配置
  renderer?: {
    width: number
    height: number
    webGLRenderer: {
      antialias: boolean // 抗锯齿
    }
  }
  
  // 场景配置
  scene?: {
    backgroundIntensity: number   // 背景亮度
    backgroundBlurriness: number  // 背景模糊度
    groupPosition: [number, number, number] // 图谱整体位置
  }
  
  // 控制器配置
  controls?: {
    minDistance: number    // 最小缩放距离
    maxDistance: number    // 最大缩放距离
    enableDamping: boolean // 启用阻尼
    // ... 更多OrbitControls配置
  }
}
```

## 📡 API接口

### 组件Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| width | number \| string | 1200 | 组件宽度 |
| height | number \| string | 675 | 组件高度 |
| config | Options | {} | 配置选项 |
| loading | 'loading' \| 'loaded' \| 'error' | 'loading' | 加载状态 |

### 组件Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| loadMore | (rootId: string, current: number, levelSize: number) | 需要加载更多数据时触发 |
| clickLabel | (id: string) | 点击节点标签时触发 |

### 组件Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| firstLoadPointsData | (pointsData: PointData[], total: number, rootId?: string) | Promise\<void\> | 首次加载数据 |
| loadMorePointsData | (pointsData: PointData[]) | Promise\<void\> | 加载更多数据 |
| injectMathJax | () | Promise\<void\> | 注入MathJax支持 |

## 🎮 启动和使用

### 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建组件库
npm run build
```

### 项目结构

```
src/
├── components/
│   └── ksgMap/
│       ├── KsgMap.vue          # 主组件
│       ├── index.ts            # 导出文件
│       ├── config/             # 配置管理
│       │   ├── index.ts        # 配置初始化
│       │   ├── scene.ts        # 场景配置
│       │   ├── camera.ts       # 相机配置
│       │   └── renderer.ts     # 渲染器配置
│       ├── core/               # 核心逻辑
│       │   ├── KsgGraph.ts     # 图数据处理
│       │   ├── loadData.ts     # 数据加载
│       │   └── renderData.ts   # 渲染逻辑
│       ├── types/              # 类型定义
│       ├── utils/              # 工具函数
│       └── hooks/              # 组合式函数
```

### 数据格式

```typescript
interface PointData {
  id: string              // 节点ID
  pointName: string       // 节点名称
  parentIds: string[]     // 父节点ID数组
  isMilestone: boolean    // 是否为里程碑节点
  status: number          // 学习状态 (0:未学习, 1:已学习)
}
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 [MIT](LICENSE) 许可证开源。

## 👥 作者

- **EndlessOrigin** - *初始开发* - [EndlessOrigin](https://github.com/EndlessOrigin)

## 📚 使用示例

### 单根节点模式示例

```vue
<template>
  <KsgMap
    :config="singleRootConfig"
    :loading="loading"
    @load-more="handleLoadMore"
    @click-label="handleNodeClick"
  />
</template>

<script setup lang="ts">
import { KsgMap, MODE } from '@endlessorigin/KsgMap'

const singleRootConfig = {
  model: MODE.Single_ROOT,
  pointsLevelPager: {
    current: 1,
    levelSize: 3
  },
  point: {
    radius: 0.8,
    space: 3
  },
  line: {
    length: 0.5,
    speed: 0.2,
    isRandom: true
  }
}
</script>
```

### 多根节点模式示例

```vue
<template>
  <KsgMap
    :config="multiRootConfig"
    :loading="loading"
    width="100%"
    height="800px"
  />
</template>

<script setup lang="ts">
const multiRootConfig = {
  model: MODE.MULTIPLE_ROOT,
  scene: {
    backgroundIntensity: 0.05,
    backgroundBlurriness: 0.1
  },
  camera: {
    position: { x: 50, y: 0, z: 30 }
  }
}
</script>
```

### 自定义样式示例

```vue
<template>
  <div class="custom-ksg-container">
    <KsgMap
      :config="customConfig"
      class="custom-ksg-map"
    />
  </div>
</template>

<style scoped>
.custom-ksg-container {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.custom-ksg-map {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
```

## 🔧 故障排除

### 常见问题

**Q: 组件无法正常渲染3D场景**
A: 检查浏览器是否支持WebGL，可以访问 https://get.webgl.org/ 测试

**Q: 节点数据加载后没有显示**
A: 确保数据格式正确，特别是 `parentIds` 字段应为数组格式

**Q: 流光效果不显示**
A: 检查 `line.speed` 配置是否大于0，以及连线是否正确建立

**Q: 组件在容器尺寸变化时显示异常**
A: 组件内置了 ResizeObserver，确保容器有明确的宽高设置

### 性能优化建议

1. **大数据集处理**：使用分层加载，避免一次性加载过多节点
2. **渲染优化**：适当调整 `levelSpace` 和 `point.space` 减少重叠
3. **动画性能**：在低性能设备上可以禁用流光效果
4. **内存管理**：及时销毁不需要的组件实例

### 调试技巧

```typescript
// 开启Three.js性能监控
import Stats from 'three/examples/jsm/libs/stats.module.js'

const stats = new Stats()
document.body.appendChild(stats.dom)

function animate() {
  stats.begin()
  // 渲染代码
  stats.end()
  requestAnimationFrame(animate)
}
```

## 🔄 版本更新日志

### v2.0.0 (2024-01-01)
- ✨ 新增多根节点模式支持
- 🎨 优化3D渲染性能
- 🔧 重构配置系统
- 📱 改进响应式设计
- 🐛 修复已知问题

### v1.x.x
- 🎯 基础单根节点模式
- 🎮 基本交互功能
- ✨ 流光动画效果

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 [MIT](LICENSE) 许可证开源。

## 👥 作者

- **EndlessOrigin** - *初始开发* - [EndlessOrigin](https://github.com/EndlessOrigin)

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Three.js](https://threejs.org/) - 3D图形库
- [Tween.js](https://github.com/tweenjs/tween.js/) - 动画库
- [Lodash](https://lodash.com/) - 实用工具库

---

如果这个项目对你有帮助，请给个 ⭐️ 支持一下！
