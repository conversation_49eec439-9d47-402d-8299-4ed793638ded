// 模拟数据生成器

/**
 * 生成随机知识点ID
 */
function generatePointId() {
  // 生成类似 K1907350322875195392 格式的ID
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000000);
  return `K${timestamp}${random}`;
}


/**
 * 生成知识点名称
 */
function generateKnowledgeName(level = 1, index = 1) {
  const subjects = [
    'Vue框架', 'React开发', 'JavaScript', 'TypeScript', 'Node.js',
    'HTML5', 'CSS3', 'Webpack', '前端工程化', '组件化开发'
  ];

  const topics = [
    '基础语法', '核心概念', '高级特性', '最佳实践', '性能优化',
    '状态管理', '路由配置', '组件通信', '生命周期', '响应式原理'
  ];

  const subtopics = [
    '基本用法', '配置选项', 'API接口', '实战案例', '常见问题',
    '调试技巧', '源码分析', '扩展插件', '工具链', '测试方法'
  ];

  if (level === 1) {
    // 根节点
    return subjects[Math.floor(Math.random() * subjects.length)];
  } else if (level === 2) {
    // 二级节点
    return `${topics[Math.floor(Math.random() * topics.length)]}`;
  } else {
    // 三级及以下节点
    return `${subtopics[Math.floor(Math.random() * subtopics.length)]}${index > 1 ? index : ''}`;
  }
}

/**
 * 生成单个知识点数据 - 符合新的数据格式
 */
function generateKnowledgePoint(pointName, level = 1, parentPointIds = []) {
  return {
    pointId: generatePointId(),
    pointName: pointName,
    status: Math.floor(Math.random() * 3), // 0, 1, 2 随机状态
    isMilestone: Math.random() > 0.8 ? 1 : null, // 20%概率为里程碑
    parentPointIds: parentPointIds
  };
}

// 删除连接生成函数，新格式不需要单独的连接数据

/**
 * 生成单根节点模拟数据 - 新格式
 */
function generateMockData(klgCode, current = 1, limit = 10) {
  console.log(`生成单根节点数据: klgCode=${klgCode}, current=${current}, limit=${limit}`);

  const records = [];
  const totalPoints = Math.min(limit * 5, 50); // 总数据量

  // 生成根节点 (仅在第一页)
  let rootPointId = null;
  if (current === 1) {
    const rootPointName = generateKnowledgeName(1);
    const rootPoint = generateKnowledgePoint(rootPointName, 1, []);
    rootPointId = rootPoint.pointId;
    records.push(rootPoint);
  } else {
    // 非第一页时，使用固定的根节点ID
    rootPointId = `K${klgCode}Root`;
  }

  // 生成子节点
  const remainingSlots = current === 1 ? limit - 1 : limit; // 第一页要减去根节点
  const startIndex = (current - 1) * limit;

  for (let i = 0; i < remainingSlots && (startIndex + i) < totalPoints; i++) {
    const pointName = generateKnowledgeName(current + 1, i + 1);
    const parentIds = rootPointId ? [rootPointId] : [];
    const point = generateKnowledgePoint(pointName, current + 1, parentIds);
    records.push(point);
  }

  return {
    current,
    limit,
    total: totalPoints,
    records
  };
}

/**
 * 生成多根节点模拟数据 - 新格式
 */
function generateMultipleRootMockData(rootId, current = 1, limit = 10) {
  console.log(`生成多根节点数据: rootId=${rootId}, current=${current}, limit=${limit}`);

  const records = [];
  const totalPoints = Math.min(limit * 6, 60); // 多根节点数据量

  // 存储根节点ID用于子节点引用
  const rootPointIds = [];

  // 生成多个根节点 (仅在第一页)
  if (current === 1) {
    const rootCount = Math.min(3, Math.floor(limit / 2)); // 根节点数量
    for (let i = 0; i < rootCount; i++) {
      const rootPointName = `${generateKnowledgeName(1)} ${i + 1}`;
      const rootPoint = generateKnowledgePoint(rootPointName, 1, []);
      rootPointIds.push(rootPoint.pointId);
      records.push(rootPoint);
    }
  } else {
    // 非第一页时，使用固定的根节点ID
    for (let i = 0; i < 3; i++) {
      rootPointIds.push(`K${rootId}Root${i}`);
    }
  }

  // 生成子节点
  const remainingSlots = current === 1 ? limit - rootPointIds.length : limit;
  const startIndex = (current - 1) * limit;

  for (let i = 0; i < remainingSlots && (startIndex + i) < totalPoints; i++) {
    const pointName = generateKnowledgeName(current + 1, i + 1);
    // 随机选择一个或多个父节点
    const parentCount = Math.floor(Math.random() * 2) + 1; // 1-2个父节点
    const selectedParents = [];
    for (let j = 0; j < parentCount && j < rootPointIds.length; j++) {
      const parentIndex = Math.floor(Math.random() * rootPointIds.length);
      if (!selectedParents.includes(rootPointIds[parentIndex])) {
        selectedParents.push(rootPointIds[parentIndex]);
      }
    }

    const point = generateKnowledgePoint(pointName, current + 1, selectedParents);
    records.push(point);
  }

  return {
    current,
    limit,
    total: totalPoints,
    records
  };
}

// CommonJS导出
module.exports = {
  generateMockData,
  generateMultipleRootMockData
};
