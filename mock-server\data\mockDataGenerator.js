// 模拟数据生成器

/**
 * 生成随机ID
 */
function generateId(prefix = 'K') {
  return `${prefix}${Date.now()}${Math.floor(Math.random() * 1000)}`;
}


/**
 * 生成知识点名称
 */
function generateKnowledgeName(level = 1, index = 1) {
  const subjects = ['数学', '物理', '化学', '生物', '计算机', '英语', '历史', '地理'];
  const topics = ['基础概念', '核心理论', '实践应用', '高级技巧', '综合分析'];
  const subtopics = ['定义', '公式', '定理', '方法', '案例', '练习', '总结'];
  
  const subject = subjects[Math.floor(Math.random() * subjects.length)];
  
  if (level === 1) {
    return `${subject}${topics[Math.floor(Math.random() * topics.length)]}`;
  } else {
    return `${subject}${subtopics[Math.floor(Math.random() * subtopics.length)]}${index}`;
  }
}

/**
 * 生成单个知识点数据
 */
function generateKnowledgePoint(id, name, level = 1, parentId = null) {
  return {
    id,
    name,
    level,
    parentId,
    // color: generateRandomColor(),
    position: {
      x: (Math.random() - 0.5) * 200,
      y: (Math.random() - 0.5) * 200,
      z: (Math.random() - 0.5) * 200
    },
    metadata: {
      difficulty: Math.floor(Math.random() * 5) + 1,
      importance: Math.floor(Math.random() * 5) + 1,
      studyTime: Math.floor(Math.random() * 120) + 30,
      tags: ['基础', '重要', '核心'][Math.floor(Math.random() * 3)]
    }
  };
}

/**
 * 生成关系连接数据
 */
function generateConnections(points) {
  const connections = [];
  
  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    
    // 为每个节点生成1-3个连接
    const connectionCount = Math.floor(Math.random() * 3) + 1;
    
    for (let j = 0; j < connectionCount && j < points.length - 1; j++) {
      const targetIndex = (i + j + 1) % points.length;
      const target = points[targetIndex];
      
      if (point.id !== target.id) {
        connections.push({
          id: `conn_${point.id}_${target.id}`,
          source: point.id,
          target: target.id,
          type: ['prerequisite', 'related', 'extends'][Math.floor(Math.random() * 3)],
          strength: Math.random() * 0.8 + 0.2
        });
      }
    }
  }
  
  return connections;
}

/**
 * 生成单根节点模拟数据
 */
function generateMockData(klgCode, current = 1, limit = 10) {
  console.log(`生成单根节点数据: klgCode=${klgCode}, current=${current}, limit=${limit}`);
  
  const points = [];
  const totalPoints = Math.min(limit * 5, 50); // 总数据量
  
  // 生成根节点
  if (current === 1) {
    const rootPoint = generateKnowledgePoint(
      klgCode || generateId(),
      `根知识点-${klgCode}`,
      1,
      null
    );
    points.push(rootPoint);
  }
  
  // 生成子节点
  const startIndex = (current - 1) * limit;
  for (let i = 0; i < limit && (startIndex + i) < totalPoints; i++) {
    const pointId = generateId();
    const pointName = generateKnowledgeName(current + 1, i + 1);
    const point = generateKnowledgePoint(pointId, pointName, current + 1, klgCode);
    points.push(point);
  }
  
  // 生成连接关系
  const connections = generateConnections(points);
  
  return {
    dataList: points,
    connections,
    total: totalPoints,
    current,
    limit,
    hasMore: (current * limit) < totalPoints,
    metadata: {
      rootId: klgCode,
      maxLevel: 5,
      generatedAt: new Date().toISOString()
    }
  };
}

/**
 * 生成多根节点模拟数据
 */
function generateMultipleRootMockData(rootId, current = 1, limit = 10) {
  console.log(`生成多根节点数据: rootId=${rootId}, current=${current}, limit=${limit}`);
  
  const points = [];
  const totalPoints = Math.min(limit * 8, 80); // 多根节点数据量更大
  
  // 生成多个根节点
  if (current === 1) {
    const rootCount = Math.min(3, limit); // 最多3个根节点
    for (let i = 0; i < rootCount; i++) {
      const rootPoint = generateKnowledgePoint(
        `${rootId}_root_${i}`,
        `根节点${i + 1}-${generateKnowledgeName(1)}`,
        1,
        null
      );
      points.push(rootPoint);
    }
  }
  
  // 生成子节点
  const remainingSlots = limit - (current === 1 ? Math.min(3, limit) : 0);
  const startIndex = current === 1 ? 0 : (current - 1) * limit;
  
  for (let i = 0; i < remainingSlots && (startIndex + i) < totalPoints; i++) {
    const pointId = generateId();
    const pointName = generateKnowledgeName(current + 1, i + 1);
    const parentId = current === 1 ? `${rootId}_root_${i % 3}` : rootId;
    const point = generateKnowledgePoint(pointId, pointName, current + 1, parentId);
    points.push(point);
  }
  
  // 生成连接关系
  const connections = generateConnections(points);
  
  return {
    dataList: points,
    connections,
    total: totalPoints,
    current,
    limit,
    hasMore: (current * limit) < totalPoints,
    metadata: {
      rootId,
      rootCount: current === 1 ? Math.min(3, limit) : 0,
      maxLevel: 6,
      generatedAt: new Date().toISOString()
    }
  };
}

// CommonJS导出
module.exports = {
  generateMockData,
  generateMultipleRootMockData
};
