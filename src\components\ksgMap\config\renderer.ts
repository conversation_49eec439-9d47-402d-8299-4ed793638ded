import * as THREE from "three";
import ctx from "../ctx";
import type { RendererConfig } from "../types/index";

export default function useRenderer(
  config: RendererConfig
) {
  const renderer = new THREE.WebGLRenderer({
    antialias: true,
    ...(config?.webGLRenderer ?? {}),
  });
  renderer.setSize(config.width, config.height);
  renderer.setPixelRatio(window.devicePixelRatio);
  ctx.renderer = renderer;
  return {
    renderer,
    rendererDom: renderer.domElement,
  };
}
