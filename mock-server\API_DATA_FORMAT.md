# KSG知识图谱API数据格式说明

## 📋 概述

本文档描述了KSG知识图谱模拟服务器返回的数据格式，完全符合您提供的API规范。

## 🔗 API接口

### 1. 单根节点知识图谱
**接口**: `POST /champaign/klg/graph`

**请求格式**:
```json
{
  "klgCodes": ["K123456"],
  "current": 1,
  "limit": 10
}
```

**响应格式**:
```json
{
  "code": "0",
  "message": "成功",
  "data": {
    "current": 1,
    "limit": 10,
    "total": 25,
    "records": [
      {
        "pointId": "K175427640590795462",
        "pointName": "JavaScript",
        "status": 2,
        "isMilestone": 1,
        "parentPointIds": []
      },
      {
        "pointId": "K175427640590812345",
        "pointName": "基础语法",
        "status": 0,
        "isMilestone": null,
        "parentPointIds": ["K175427640590795462"]
      }
    ]
  },
  "success": true
}
```

### 2. 多根节点知识图谱
**接口**: `POST /champaign/klg/prjGraph`

**请求格式**:
```json
{
  "klgCodes": ["1"],
  "current": 1,
  "limit": 8
}
```

**响应格式**:
```json
{
  "code": "0",
  "message": "成功",
  "data": {
    "current": 1,
    "limit": 8,
    "total": 48,
    "records": [
      {
        "pointId": "K175427640590795462",
        "pointName": "Vue框架 1",
        "status": 1,
        "isMilestone": null,
        "parentPointIds": []
      },
      {
        "pointId": "K175427640590795463",
        "pointName": "React开发 2",
        "status": 0,
        "isMilestone": null,
        "parentPointIds": []
      },
      {
        "pointId": "K175427640590795464",
        "pointName": "核心概念",
        "status": 2,
        "isMilestone": 1,
        "parentPointIds": ["K175427640590795462", "K175427640590795463"]
      }
    ]
  },
  "success": true
}
```

## 📊 数据字段说明

### 响应结构
- **code**: 状态码，"0"表示成功，其他表示错误
- **message**: 响应消息，如"成功"
- **data**: 数据对象，包含分页信息和记录列表
- **success**: 布尔值，表示请求是否成功

### 分页信息
- **current**: 当前页码
- **limit**: 每页记录数
- **total**: 总记录数
- **records**: 知识点记录数组

### 知识点记录 (Record)
- **pointId**: 知识点唯一标识符，格式如"K175427640590795462"
- **pointName**: 知识点名称，如"JavaScript"、"基础语法"等
- **status**: 知识点状态
  - `0`: 未开始
  - `1`: 进行中  
  - `2`: 已完成
- **isMilestone**: 是否为里程碑
  - `1`: 是里程碑
  - `null`: 不是里程碑
- **parentPointIds**: 父知识点ID数组
  - 空数组`[]`: 根节点
  - 包含ID: 子节点，如`["K175427640590795462"]`

## 🎯 数据特性

### 单根节点模式
- 第一页包含1个根节点 + (limit-1)个子节点
- 后续页面只包含子节点
- 所有子节点的`parentPointIds`都指向根节点

### 多根节点模式  
- 第一页包含2-3个根节点 + 剩余的子节点
- 子节点可能有1-2个父节点
- 支持复杂的父子关系网络

### 随机数据生成
- **pointId**: 基于时间戳+随机数生成，确保唯一性
- **pointName**: 从预定义的技术术语中随机选择
- **status**: 随机分配0-2的状态值
- **isMilestone**: 20%概率设为里程碑
- **parentPointIds**: 根据节点层级智能分配父节点

## 🔧 自定义配置

### 修改知识点名称
编辑 `data/mockDataGenerator.js` 中的 `generateKnowledgeName` 函数：
```javascript
const subjects = [
  'Vue框架', 'React开发', 'JavaScript', 'TypeScript', 'Node.js',
  // 添加更多主题...
];
```

### 调整数据量
- **总数据量**: 修改 `totalPoints` 计算逻辑
- **根节点数量**: 修改多根节点模式中的 `rootCount`
- **父子关系**: 调整 `parentCount` 和选择逻辑

## ✅ 验证测试

运行测试脚本验证数据格式：
```bash
node test-api.js
```

测试结果示例：
```
✅ 单根节点API响应:
   - 状态码: 0
   - 消息: 成功
   - 成功标志: true
   - 记录数量: 5
   - 第一个记录:
     * pointId: K175427640590795462
     * pointName: JavaScript
     * status: 2
     * isMilestone: 1
     * parentPointIds: []
```

## 🎉 总结

新的数据格式完全符合您的API规范：
- ✅ 正确的响应结构 (code, message, data, success)
- ✅ 标准的分页信息 (current, limit, total, records)
- ✅ 完整的知识点字段 (pointId, pointName, status, isMilestone, parentPointIds)
- ✅ 智能的父子关系处理
- ✅ 丰富的随机测试数据

现在您的前端应用可以无缝对接这个模拟服务器，获得真实的API体验！🚀
