<script setup lang="ts">
// import { MODE, type Options } from "@endlessorigin/KsgMap";
import { KsgMap, MODE, type Options } from "./components/ksgMap";
import { ref, onMounted } from "vue";
import Stats from "three/examples/jsm/libs/stats.module.js";
import testAPI, {
  multiplyRootsAPI,
  localModeApi,
  localMode2Api,
} from "./network/api";

const request = localMode2Api;
//模拟一个领域的数据
const container = ref<HTMLElement>();
const stats = new Stats();
const ksgRef = ref<any>();
let loading = ref<"loading" | "loaded" | "error">("loading");
// 场景配置
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //单根节点模式或多根节点模式
  // 配置分层加载
  pointsLevelPager: {
    current: 1, //当前层
    levelSize: 1, //获取多少层
  },
};

async function init() {
  stats.showPanel(0);
  stats.dom.style.position = "absolute";
  stats.dom.style.transform = "scale(2)";
  stats.dom.style.zIndex = "10";
  stats.dom.style.top = "30px";
  stats.dom.style.left = "40px";
  container.value?.appendChild(stats.dom);
  update();

  loading.value = "loading";
  const {
    // @ts-ignore
    data: { dataList, total },
    /**
     * 这里测试只获取一层单个根节点
     */
  } = await request(1, 1, "1");
  ksgRef.value?.firstLoadPointsData(dataList, total, "1");
  loading.value = "loaded";
}

function update() {
  requestAnimationFrame(update);
  stats.update();
}
onMounted(init);
async function handleLoadMore(rootId: string, crt: number, levelSize: number) {
  loading.value = "loading";
  const {
    // @ts-ignore
    data: { dataList, total },
  } = await request(crt, levelSize, rootId);
  ksgRef.value?.loadMorePointsData(dataList);
  loading.value = "loaded";
}
function handleClickLabel(id: string) {
  alert(id);
}
</script>

<template>
  <div class="container" ref="container">
    <!-- @vue-ignore -->
    <KsgMap
      ref="ksgRef"
      :config="config"
      @load-more="handleLoadMore"
      :loading="loading"
      @click-label="handleClickLabel"
    />
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
</style>
