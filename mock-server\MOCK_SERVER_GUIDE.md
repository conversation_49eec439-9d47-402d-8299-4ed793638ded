# KSG知识图谱模拟服务器使用指南

## 📋 项目概述

已成功为您的KSG知识图谱项目创建了一个完整的Node.js后端模拟服务，用于提供本地测试数据。

## 🎯 功能特性

- ✅ **单根节点模式**：模拟单个根知识点的层级结构
- ✅ **多根节点模式**：模拟多个根知识点的复杂图谱  
- ✅ **分页支持**：支持分页加载大量数据
- ✅ **关系生成**：自动生成知识点之间的连接关系
- ✅ **随机数据**：生成丰富的模拟数据用于测试
- ✅ **CORS支持**：完全支持跨域请求
- ✅ **错误处理**：完善的错误处理和日志记录

## 🚀 快速启动

### 1. 启动模拟服务器

```bash
# 进入mock-server目录
cd mock-server

# 启动服务器
npm start
```

服务器将在 `http://localhost:3000` 启动并显示：
```
🚀 KSG知识图谱模拟服务器启动成功！
📍 服务地址: http://localhost:3000
🔗 健康检查: http://localhost:3000/health
📊 单根节点API: POST /champaign/klg/graph
📊 多根节点API: POST /champaign/klg/prjGraph
```

### 2. 启动前端应用

```bash
# 在项目根目录
npm run dev
```

前端将通过Vite代理自动连接到模拟服务器。

## 📊 API接口详情

### 单根节点知识图谱
- **路径**: `POST /champaign/klg/graph`
- **用途**: 获取单个根知识点的层级数据

**请求示例**:
```json
{
  "klgCodes": ["K123456"],
  "current": 1,
  "limit": 10
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "dataList": [...],
    "connections": [...],
    "total": 50,
    "current": 1,
    "limit": 10,
    "hasMore": true
  }
}
```

### 多根节点知识图谱
- **路径**: `POST /champaign/klg/prjGraph`
- **用途**: 获取多个根知识点的复杂图谱数据

**请求示例**:
```json
{
  "klgCodes": ["1"],
  "current": 1,
  "limit": 8
}
```

## 🔧 配置说明

### Vite代理配置
您的 `vite.config.ts` 已正确配置：
```javascript
server: {
  proxy: {
    "/local": {
      target: "http://127.0.0.1:3000/",
      secure: false,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/local/, ""),
    },
  },
}
```

### 前端API调用
前端通过以下函数调用本地API：
- `localModeApi()` - 单根节点
- `localMode2Api()` - 多根节点

## 🧪 测试验证

### 运行API测试
```bash
cd mock-server
node test-api.js
```

### 健康检查
访问: `http://localhost:3000/health`

## 📁 项目结构

```
mock-server/
├── package.json          # 项目配置
├── simple-server.js      # 主服务器文件
├── server.js            # Express版本(备用)
├── test-api.js          # API测试脚本
├── start.bat            # Windows启动脚本
├── start.sh             # Linux/Mac启动脚本
├── README.md            # 详细文档
└── data/
    └── mockDataGenerator.js  # 数据生成逻辑
```

## 🎨 数据结构

### 知识点节点
```json
{
  "id": "K1704649123456",
  "name": "数学基础概念",
  "level": 1,
  "parentId": null,
  "color": "#FF6B6B",
  "position": { "x": 10.5, "y": -20.3, "z": 15.7 },
  "metadata": {
    "difficulty": 3,
    "importance": 4,
    "studyTime": 90,
    "tags": "核心"
  }
}
```

### 连接关系
```json
{
  "id": "conn_K123_K456",
  "source": "K123",
  "target": "K456",
  "type": "prerequisite",
  "strength": 0.8
}
```

## 🔄 使用流程

1. **启动服务器**: `cd mock-server && npm start`
2. **启动前端**: `npm run dev`
3. **访问应用**: 前端自动连接模拟数据
4. **测试功能**: 验证3D图谱渲染和交互

## 🛠️ 自定义配置

### 修改数据生成规则
编辑 `mock-server/data/mockDataGenerator.js` 来自定义：
- 知识点名称生成规则
- 节点颜色和位置
- 连接关系逻辑
- 数据量和复杂度

