import clonePoint from "../utils/clonePoint";
import type { PointData, Point } from "../types";
import FrameScheduler from "../utils/FrameScheduler";
export const frameScheduler = new FrameScheduler(); //任务调度器

/** 发生变化的节点*/
export type ModifyPoint = {
  /*更新前 */
  old: Point;
  /**更新后 */
  new?: Point;
};

/**diff数据 */
export type DiffData = {
  /**最新数据 */
  newPoints: Point[];
  /**更新的数据 */
  updatePoints: Map<string, ModifyPoint>; //id-point
};

/**
 * DAG空间坐标计算
 */
export default class KsgGraph {
  /*处理后维护节点数据 */
  pointsData: Map<string, Point> = new Map();
  /*层级map */
  idLevelMap: { [level: number]: string[] } = {};

  /**加载更多diff数据*/
  diffData: DiffData = { newPoints: [], updatePoints: new Map() };

  /**
   * @param pointsData 节点数据
   * @param levelSpace 层级间隔
   * @param pointSpace 点间隔
   */
  constructor(pointsData: PointData[]) {
    this.compute(pointsData);
  }

  /**
   * @param pointsData 节点数据
   */
  compute(pointsData: PointData[]) {
    this.build(pointsData);
    this.computeLevel(this.pointsData);
    frameScheduler.addTask(() => {
      this.computePointPosition();
      return false;
    });
  }
  /**
   * 加载更多数据
   * @param pointsData 节点数据
   */
  loadMore(pointsData: PointData[]): Promise<DiffData> {
    const diffData: DiffData = {
      newPoints: [],
      updatePoints: new Map(),
    };
    this.diffData = diffData;

    pointsData
      .map((point) => {
        let oldPoint = this.pointsData.get(point.pointId)!;
        if (oldPoint) {
          oldPoint = clonePoint(oldPoint);
          diffData.updatePoints.set(oldPoint.id, {
            old: oldPoint,
          });
        }
        return point?.pointId ?? null;
      })
      .filter((point) => point);
    //处理数据
    this.build(pointsData);
    this.computeLevel(this.pointsData);

    frameScheduler.addTask(() => {
      this.computePointPosition();
      return false;
    });
    // 位置计算完处理
    return new Promise((resolve) => {
      frameScheduler.onCompleted(() => {
        pointsData.forEach(({ pointId }) => {
          if (diffData.updatePoints.has(pointId)) {
            diffData.updatePoints.get(pointId)!.new =
              this.pointsData.get(pointId)!;
          } else {
            diffData.newPoints.push(this.pointsData.get(pointId)!);
          }
        });
        // console.log("diffData", diffData);
        resolve(diffData);
      });
    });
  }

  /**
   * 把数据维护到pointsDataMap中
   * 父关系转为子关系
   * @param pointsData 节点数据
   */
  private build(pointsData: PointData[]) {
    frameScheduler.addTask(() => {
      for (const point of pointsData) {
        let index = -1;
        const oldPoint = this.pointsData.get(point.pointId);
        if (oldPoint) {
          const cloneOldPoint = clonePoint(oldPoint);
          this.diffData.updatePoints.set(point.pointId, {
            old: cloneOldPoint,
          });
          oldPoint!.parentIds.push(...point.parentPointIds);
        } else {
          this.pointsData.set(point.pointId, {
            id: point.pointId,
            name: point.pointName,
            parentIds: point.parentPointIds,
            childIds: [],
            level: -1,
            coordinate: [0, 0, 0],
            isMilestone: point.isMilestone,
            status: point.status,
            index,
          });
        }
      }
      return false;
    });
    frameScheduler.addTask(() => {
      for (const point of pointsData) {
        const { pointId, parentPointIds } = point;
        for (const parentId of parentPointIds) {
          this.pointsData.get(parentId)?.childIds.push(pointId);
        }
      }
      return false;
    });
  }

  /**
   * 计算层级
   */
  computeLevel(points: Map<string, Point>) {
    this.idLevelMap = {};
    let degrees: { [key: string]: number } = {};
    let queue: string[] = [];
    const scope = this;

    // const taskCount = 10; // 分出的任务数量
    // frameScheduler.addTask(() => {

    //   return false;
    // });
    /**
     * 使用一个task来进行任务分块
     */

    frameScheduler.addTask(() => {
      scope.idLevelMap = {};
      degrees = {};
      queue = [];
      scope.pointsData.forEach((point) => {
        for (const childId of point.childIds) {
          if (!degrees[childId]) degrees[childId] = 0;
          degrees[childId]++;
        }

        return false;
      });

      return false;
    });

    // 计算第一层
    let startLevel = 0;
    frameScheduler.addTask(() => {
      scope.pointsData.forEach((point) => {
        const { id } = point;
        if (!degrees[id]) {
          queue.push(id);
          point.level = startLevel;
          if (!this.idLevelMap[startLevel]) this.idLevelMap[startLevel] = [];
          this.idLevelMap[startLevel].push(id);
        }
      });

      return false;
    });

    frameScheduler.addTask(() => {
      // 遍历所有节点，计算层级
      while (queue.length) {
        const id = queue.shift()!;
        const point = scope.pointsData.get(id)!;
        for (const childId of point.childIds) {
          degrees[childId]--;
          if (degrees[childId] === 0) {
            const crtLevel = scope.pointsData.get(id)!.level + 1;
            points.get(childId)!.level = crtLevel;
            queue.push(childId);
            if (!scope.idLevelMap[crtLevel]) scope.idLevelMap[crtLevel] = [];

            scope.idLevelMap[crtLevel].push(childId); //维护到idLevelMap中
          }
        }
      }
      // 判环
      if (Object.values(degrees).some((item) => item > 0)) {
        throw new Error("当前数据结构存在环");
      }
      return false;
    });
  }

  /**
   * 计算每层节点的位置
   */
  computePointPosition(levelHeight: number = 15, pointSpace: number = 7) {
    const levels = Object.keys(this.idLevelMap).map((key) => Number(key));
    levels.forEach((level, index) => {
      frameScheduler.addTask(() => {
        const crtLevelPointIds = this.idLevelMap[level];
        let y = -level * levelHeight;

        let count = crtLevelPointIds.length;

        if (count === 1) {
          const id = crtLevelPointIds[0];

          if (
            this.diffData.updatePoints.has(id) &&
            this.isPositionChange(id, [0, y, 0])
          ) {
            this.diffData.updatePoints.get(id)!.new = this.pointsData.get(id)!;
          } else if (this.isPositionChange(id, [0, y, 0])) {
            this.diffData.updatePoints.set(id, {
              old: clonePoint(this.pointsData.get(id)!),
              new: this.pointsData.get(id)!,
            });
          }
          this.pointsData.get(id)!.coordinate = [0, y, 0];
        } else {
          const interval = 3;
          let index = 0;
          for (let round = 1, s = count; s != 0; ++round) {
            let num = interval * round;
            if (num < s) {
              s -= num;
            } else {
              num = s;
              s = 0;
            }
            //重新计算y值
            y += Math.sin(round);
            const r = round * pointSpace;
            for (let i = 0; i < num; ++i) {
              const x = r * Math.cos((2 * Math.PI * i) / num);
              const z = r * Math.sin((2 * Math.PI * i) / num);
              if (index < count) {
                const id = crtLevelPointIds[index++];
                if (
                  this.diffData.updatePoints.has(id) &&
                  this.isPositionChange(id, [x, y, z])
                ) {
                  this.diffData.updatePoints.get(id)!.new =
                    this.pointsData.get(id)!;
                } else if (this.isPositionChange(id, [x, y, z])) {
                  this.diffData.updatePoints.set(id, {
                    old: clonePoint(this.pointsData.get(id)!),
                    new: this.pointsData.get(id)!,
                  });
                }
                this.pointsData.get(id)!.coordinate = [x, y, z];
              }
            }
          }
        }
        return index === levels.length - 1;
      });
    });
  }

  /**
   * 是否发生位置变化
   */
  private isPositionChange(id: string, coordinate: [number, number, number]) {
    const [x, y, z] = this.pointsData.get(id)?.coordinate ?? [0, 0, 0];
    if (
      (!coordinate[0] && !coordinate[1] && !coordinate[2]) ||
      (!x && !y && !z)
    )
      return false;
    return x !== coordinate[0] || y !== coordinate[1] || z !== coordinate[2];
  }

  /**
   * 根据id获取Point
   */
  getPointById(id: string): Point | null {
    return this.pointsData.get(id) ?? null;
  }

  /**
   * 获取当前图的最大层级
   * 从0层起
   */
  getMaxLevel() {
    return Math.max(...Object.keys(this.idLevelMap).map((key) => Number(key)));
  }
}
