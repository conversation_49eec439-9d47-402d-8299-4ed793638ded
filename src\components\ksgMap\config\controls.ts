import { KsgControls } from "../core/KsgControls";
import type {ControlsConfig } from "../types/index"
import ctx from "../ctx";
export default function useControls(option:ControlsConfig ) {
  const controls = new KsgControls(
    ctx.camera!,
    ctx.viewGroup!,
    ctx.css2dRenderer?.domElement! //注意应渲染css2Drenderer的domElement，否则受层级影响无法使用轨道控制器
  );
  controls.object.position.set(...option.position);
  // 相机朝向
  controls.target.set(...option.target);


  // 极角范围
  controls.minPolarAngle = option.minPolarAngle;
  controls.maxPolarAngle = option.maxPolarAngle;

  // 相机距离
  controls.minDistance = option.minDistance;
  controls.maxDistance = option.maxDistance;
 // 鼠标控制
  controls.mouseButtons = option.mouseButtons;
  controls.enableDamping = option.enableDamping;
  // 限制 y 轴范围在 yRange 以内
  controls.yMinRange = option.yMinRange;
  controls.yMaxRange = option.yMaxRange;
  controls.yDelta = option.yDelta;
  //   挂载上下文
  ctx.controls = controls;

  return { controls };
}
