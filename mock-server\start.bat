@echo off
chcp 65001 >nul

echo 🚀 启动KSG知识图谱模拟服务器...

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 请在mock-server目录下运行此脚本
    pause
    exit /b 1
)

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
)

REM 启动服务器
echo 🎯 启动服务器...
npm start

pause
