import { Vector3 } from "three";
import { throttle } from "lodash";
import ctx from "../ctx";
import { getDomElement, findValidateParentNode } from "../utils";
import innerViewValidate from "../utils/viewValidate";
import createClickPointEvent from "../utils/clickPointEvent";
import { handleEnterFocus } from "../core/enterFocus";
import { ENTER_FOCUS_MODE, VIEW_MODE, LOAD_STATUS } from "../enums";
import enterGlobalView from "../core/enterGlobalView";
import { createMouseMoveEvent } from "../utils/globalViewEvent";
import createSceneHover from "../utils/hoverObjectEvent";
import type { EventsCallback, Point, Size } from "../types";
import ksgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { hoverLabel, focusLabel } from "../core/KsgLabel";
import TWEEN from "@tweenjs/tween.js";

/**
 * 初始化事件
 */
export default function useInitEvents(
  wrapperElSize: Size,
  events: EventsCallback
) {
  // 容器元素
  let wrapperEle: HTMLElement | null;
  ctx.viewGroup?.add(ksgHover);
  ctx.viewGroup?.add(focusCrust);
  ctx.viewGroup?.add(hoverLabel);
  ctx.viewGroup?.add(focusLabel);

  let enableHover = true;
  let {
    clear,
    updateSize: updateHoverEventSize,
    event,
  } = createSceneHover(
    wrapperElSize,
    ctx,
    (point: Point) => {
      const pointDnc = ctx.pointsMesh?.getWorldP(
        point.index!,
        ctx.camera!,
        wrapperElSize.width * window.devicePixelRatio,
        wrapperElSize.height * window.devicePixelRatio
      )!;
      if (
        ctx.pointsMesh?.focusIndex === point.index ||
        ctx.pointsMesh?.lastHoverIndex === point.index ||
        !enableHover ||
        !innerViewValidate(ctx.viewRange!, { ...pointDnc })
      )
        return;
      ctx.pointsMesh?.toggleHover(point.index!);
      ksgHover.display(point);
      hoverLabel.display(point, {
        viewRange: ctx.viewRange!,
        dnc: pointDnc,
      });
      document.body.style.cursor = "pointer";
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
        ctx.controls!.autoRotate = false;
        clearMoveEvent();
      }
    },
    () => {
      ksgHover.hide();
      hoverLabel.hide();
      ctx.pointsMesh?.toggleHover();
      document.body.style.cursor = "default";
    }
  );

  // 创建节点点击事件进入子图
  let {
    clear: clearFocusEvent,
    updateSize: updateClickEventSize,
    event: handleFocusEvent,
  } = createClickPointEvent(wrapperElSize, ctx, (point: Point) => {
    if (
      (!ctx.controls?.enabled && point) ||
      ctx.pointsMesh?.focusIndex === point.index ||
      !innerViewValidate(
        ctx.viewRange!,
        ctx.pointsMesh?.getWorldP(
          point.index!,
          ctx.camera!,
          wrapperElSize.width,
          wrapperElSize.height
        )!
      )
    )
      return;
    // 切换模式
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      ctx.controls!.autoRotate = false;
      ctx.viewMode = VIEW_MODE.Focus_VIEW;
      ctx.pointsMesh!.breathAnimationSwitch(false);
    }
    enableHover = false;
    ksgHover.hide();
    hoverLabel.hide();
    focusLabel.hide();
    TWEEN.removeAll(); //清空上一次未执行完的动画
    focusCrust.display(point);
    ctx.pointsMesh?.toggleFocus(point.index!);
    handleEnterFocus(point).then(() => {
      enableHover = true;
    });
  });

  //加载更多知识节点数据事件
  function handleLoadMore(e: any) {
    const { y } = e.target.target;
    // return;
    if (
      y - 1.9 < -ctx.maxLevelY! &&
      ctx.loadStatus === LOAD_STATUS.loaded &&
      ctx.graph!.pointsData.size < ctx.pointsLevelPager!.total!
    ) {
      events?.loadMoreCallback?.(
        ctx.focusPointInfo?.pointId!,
        ctx.pointsLevelPager!.current,
        ctx.pointsLevelPager?.levelSize!
      );
    }
  }

  /**
   * 外部异步结果来修改该加载状态
   */
  function changeLoadStatus(status: LOAD_STATUS) {
    if (
      status === LOAD_STATUS.loaded &&
      ctx.graph?.pointsData.size! < ctx.pointsLevelPager?.total!
    ) {
      ctx.pointsLevelPager!.current += ctx.pointsLevelPager?.levelSize!;
    }

    ctx.loadStatus = status;
  }

  // 监听根节点到相机的距离
  function handleRootToCameraDistance() {
    ctx.isControls = false;
    if (!ctx.pointsMesh || !focusLabel || !focusLabel.visible) return;
    const target = new Vector3(
      ...ctx.pointsMesh!.getPointData(ctx.pointsMesh!.focusIndex)!.coordinate
    );
    target.y += 6;
    const distance = target?.distanceTo(ctx.camera?.position!);
    // 控制label显示
    focusLabel.distanceShow(distance < (ctx.maxDistance ?? 100));
  }

  // 处理标签点击
  function handleLabelClick(e: MouseEvent) {
    if (e.button !== 0) return;
    const dom = getDomElement(e);
    if (dom.nodeName === "DIV") return;
    const id = findValidateParentNode(dom);
    if (id) events?.clickLabelCallback?.(id);
  }

  //处理双击点击进入全局视角
  function handleEnterGlobalView(_: MouseEvent) {
    if (ctx.focusStack![ctx.focusStack!.length - 1] === "null") return;
    const { y: cameraY } = ctx.camera?.position!;
    const peerLevelsY = Object.keys(ctx.graph!.idLevelMap).map(
      (level) =>
        ctx.graph?.getPointById(ctx.graph!.idLevelMap[Number(level)][0]!)
          ?.coordinate[1]
    );

    //计算视角移动的最终位置
    const rang = ctx.levelSpace! / 2;
    let resultY =
      peerLevelsY.length > 1
        ? peerLevelsY?.find(
            (item) => cameraY > item! - rang && cameraY < item! + rang
          )
        : peerLevelsY[0];

    // 多层节点
    if (peerLevelsY.length > 1) {
      //超出最顶部
      if (!resultY && cameraY > peerLevelsY![0]!) {
        resultY = peerLevelsY![1]!;
        //  超出最底部
      } else if (
        !resultY &&
        cameraY < peerLevelsY![peerLevelsY?.length! - 1]!
      ) {
        resultY = peerLevelsY![peerLevelsY?.length! - 2]!;
      }
    }

    // 方向向量
    const offset = ctx.camera?.position
      .clone()
      .sub(new Vector3(0, resultY!, 0));
    //缩放倍率
    offset?.multiplyScalar(2);

    focusLabel.hide();
    enterGlobalView([offset!.x, resultY!, offset!.z])?.then(() => {
      ctx.controls!.autoRotate = true;
      ctx.controls!.autoRotateSpeed = 0.02;
      ctx.controls!.enabled = true;
      enableHover = true;
      wrapperEle!.addEventListener("mousemove", handleMoveEvent);
    });
  }
  const throttleHandleEnterGlobalView = throttle(handleEnterGlobalView, 500);
  /**
   *处理全局视角下相机控制器自动旋转相关事件
   */
  function onMove() {
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
      ctx.controls!.autoRotate = false;
    }
  }
  function onMoveEnd() {
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
      ctx.controls!.autoRotate = true;
    }
  }
  const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
    onMove,
    onMoveEnd,
    3000 //3秒等待时间
  );

  const controlsStart = () => (ctx.isControls = true);

  /**
   * 初始化事件
   */
  function initEvents(containerEle: HTMLElement) {
    wrapperEle = containerEle;
    containerEle.addEventListener("mousemove", event);
    containerEle.addEventListener("pointerdown", handleFocusEvent);
    containerEle.addEventListener("dblclick", throttleHandleEnterGlobalView);
    ctx.controls?.addEventListener("start", controlsStart);
    ctx.controls?.addEventListener("end", handleLoadMore);
    ctx.controls?.addEventListener("end", handleRootToCameraDistance);
    ctx.css2dRenderer?.domElement.addEventListener(
      "pointerdown",
      handleLabelClick
    );
  }
  /**
   * 销毁事件
   */
  function destroyEvents() {
    clear();
    clearFocusEvent();
    clearMoveEvent();
    wrapperEle!.removeEventListener("click", handleFocusEvent);
    wrapperEle!.removeEventListener("dblclick", throttleHandleEnterGlobalView);
    ctx.controls?.removeEventListener("start", controlsStart);
    ctx.controls?.removeEventListener("end", handleLoadMore);
    ctx.controls?.removeEventListener("end", handleRootToCameraDistance);
    ctx.css2dRenderer?.domElement.removeEventListener(
      "click",
      handleLabelClick
    );
  }

  /**
   *回退到根节点
   */
  function focusBackToRoot() {
    if (ctx.focusStack!.length === 1) return;

    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      wrapperEle!.removeEventListener("mousemove", handleMoveEvent);
      ctx.pointsMesh?.breathAnimationSwitch(false);
    }
    const rootPoint = ctx.pointsMesh!.getPointDataById(ctx.focusStack![0])!;
    TWEEN.removeAll();
    focusLabel.display(rootPoint);
    handleEnterFocus(rootPoint);
  }

  /**
   * 聚焦节点历史回退
   */
  function focusBack() {
    if (ctx.focusStack!.length < 2) return;
    ctx.focusStack?.pop();
    // 把进入全局的历史记录忽略
    if (ctx.focusStack![ctx.focusStack!.length - 1] === "null")
      ctx.focusStack?.pop();
    const point = ctx.pointsMesh!.getPointDataById(
      ctx.focusStack![ctx.focusStack!.length - 1]
    );
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      wrapperEle!.removeEventListener("mousemove", handleMoveEvent);
      ctx.pointsMesh?.breathAnimationSwitch(false);
    }
    TWEEN.removeAll();
    focusLabel.display(point!);
    handleEnterFocus(point!, ENTER_FOCUS_MODE.BACK);
  }

  return {
    initEvents,
    destroyEvents,
    focusBackToRoot,
    focusBack,
    updateClickEventSize,
    updateHoverEventSize,
    changeLoadStatus,
  };
}
