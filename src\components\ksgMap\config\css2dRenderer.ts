import { CSS2DRenderer } from "three/addons/renderers/CSS2DRenderer.js";
import ctx from "../ctx";
import type { RendererConfig } from "../types";

type Config = {
  width: number;
  height: number;
  container: HTMLElement;
};

export default function useCSS2DRender(config: Config | RendererConfig) {
  if (Object.keys(config).length < 5) {
    const css2DRenderer = new CSS2DRenderer();

    css2DRenderer.setSize(config.width, config.height);
    css2DRenderer.domElement.style.position = "absolute";
    css2DRenderer.domElement.style.top = "0px";
    if ((config as Config).container)
      (config as Config).container.appendChild(css2DRenderer.domElement);
    ctx.css2dRenderer = css2DRenderer;
  } else {
    const css2DRenderer = new CSS2DRenderer();
    css2DRenderer.setSize(config.width, config.height);
    css2DRenderer.domElement.style.position = "absolute";
    css2DRenderer.domElement.style.top = "0px";
    (config as RendererConfig).css2DRenderer?.domElement.appendChild(
      css2DRenderer.domElement
    );
    ctx.css2dRenderer = css2DRenderer;
  }
  return {
    css2dRendererDom: ctx.css2dRenderer?.domElement,
  };
}
