import {
  Scene,
  Group,
  Color,
  TextureLoader,
  EquirectangularReflectionMapping,
} from "three";
import ctx from "../ctx";
import type { Config, SceneConfig } from "../types";
import bgImg from "../assets/images/bc4.png";
export default function userScene(config: SceneConfig) {
  const texLoader = new TextureLoader();
  const texture = texLoader.load(bgImg);
  texture.mapping = EquirectangularReflectionMapping;
  const scene = new Scene();
  scene.environment = texture;
  scene.background = texture;
  scene.backgroundIntensity = config.backgroundIntensity;
  scene.backgroundBlurriness = config.backgroundBlurriness;
  const group = new Group();
  group.position.set(...config.groupPosition);
  scene.add(group);
  ctx.scene = scene;
  ctx.viewGroup = group;
  return { scene };
}
