// API测试脚本
const http = require('http');

// 发送POST请求的辅助函数
function sendPostRequest(path, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

// 测试函数
async function testAPIs() {
  console.log('🧪 开始测试KSG知识图谱模拟API...\n');
  
  try {
    // 测试单根节点API
    console.log('📊 测试单根节点API...');
    const singleRootResponse = await sendPostRequest('/champaign/klg/graph', {
      klgCodes: ['K123456'],
      current: 1,
      limit: 5
    });
    
    console.log('✅ 单根节点API响应:');
    console.log(`   - 状态码: ${singleRootResponse.code}`);
    console.log(`   - 消息: ${singleRootResponse.message}`);
    console.log(`   - 数据点数量: ${singleRootResponse.data.dataList.length}`);
    console.log(`   - 连接数量: ${singleRootResponse.data.connections.length}`);
    console.log(`   - 总数: ${singleRootResponse.data.total}`);
    console.log(`   - 是否有更多: ${singleRootResponse.data.hasMore}\n`);
    
    // 测试多根节点API
    console.log('🌐 测试多根节点API...');
    const multiRootResponse = await sendPostRequest('/champaign/klg/prjGraph', {
      klgCodes: ['1'],
      current: 1,
      limit: 8
    });
    
    console.log('✅ 多根节点API响应:');
    console.log(`   - 状态码: ${multiRootResponse.code}`);
    console.log(`   - 消息: ${multiRootResponse.message}`);
    console.log(`   - 数据点数量: ${multiRootResponse.data.dataList.length}`);
    console.log(`   - 连接数量: ${multiRootResponse.data.connections.length}`);
    console.log(`   - 总数: ${multiRootResponse.data.total}`);
    console.log(`   - 根节点数量: ${multiRootResponse.data.metadata.rootCount}`);
    console.log(`   - 是否有更多: ${multiRootResponse.data.hasMore}\n`);
    
    // 测试分页功能
    console.log('📄 测试分页功能...');
    const pageResponse = await sendPostRequest('/champaign/klg/graph', {
      klgCodes: ['K123456'],
      current: 2,
      limit: 3
    });
    
    console.log('✅ 分页API响应:');
    console.log(`   - 当前页: ${pageResponse.data.current}`);
    console.log(`   - 每页数量: ${pageResponse.data.limit}`);
    console.log(`   - 实际返回数量: ${pageResponse.data.dataList.length}\n`);
    
    // 测试错误处理
    console.log('❌ 测试错误处理...');
    const errorResponse = await sendPostRequest('/champaign/klg/graph', {
      klgCodes: [],
      current: 1,
      limit: 5
    });
    
    console.log('✅ 错误处理响应:');
    console.log(`   - 状态码: ${errorResponse.code}`);
    console.log(`   - 错误消息: ${errorResponse.message}\n`);
    
    console.log('🎉 所有测试完成！API服务正常工作。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testAPIs();
