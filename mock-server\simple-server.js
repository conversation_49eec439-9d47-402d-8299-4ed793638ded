const http = require('http');
const url = require('url');
const { generateMockData, generateMultipleRootMockData } = require('./data/mockDataGenerator.js');

const PORT = 3000;

// CORS头部
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json; charset=utf-8'
};

// 解析JSON请求体
function parseRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// 发送JSON响应
function sendJsonResponse(res, statusCode, data) {
  res.writeHead(statusCode, corsHeaders);
  res.end(JSON.stringify(data, null, 2));
}

// 日志中间件
function logRequest(req) {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  logRequest(req);
  
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200, corsHeaders);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  try {
    // 单根节点知识图谱API
    if (pathname === '/champaign/klg/graph' && req.method === 'POST') {
      const body = await parseRequestBody(req);
      console.log('单根节点请求体:', JSON.stringify(body, null, 2));
      
      const { klgCodes, current = 1, limit = 10 } = body;
      
      if (!klgCodes || !Array.isArray(klgCodes) || klgCodes.length === 0) {
        return sendJsonResponse(res, 400, {
          code: 400,
          message: '参数错误：klgCodes必须是非空数组',
          data: null
        });
      }
      
      const klgCode = klgCodes[0];
      const mockData = generateMockData(klgCode, current, limit);
      
      sendJsonResponse(res, 200, {
        code: 200,
        message: '成功',
        data: mockData
      });
      return;
    }
    
    // 多根节点知识图谱API
    if (pathname === '/champaign/klg/prjGraph' && req.method === 'POST') {
      const body = await parseRequestBody(req);
      console.log('多根节点请求体:', JSON.stringify(body, null, 2));
      
      const { klgCodes, current = 1, limit = 10, spuId } = body;
      
      // 兼容两种参数格式
      const rootId = klgCodes ? klgCodes[0] : spuId || '1';
      const mockData = generateMultipleRootMockData(rootId, current, limit);
      
      sendJsonResponse(res, 200, {
        code: 200,
        message: '成功',
        data: mockData
      });
      return;
    }
    
    // 健康检查接口
    if (pathname === '/health' && req.method === 'GET') {
      sendJsonResponse(res, 200, {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'ksg-map-mock-server'
      });
      return;
    }
    
    // 404处理
    sendJsonResponse(res, 404, {
      code: 404,
      message: `接口不存在: ${req.method} ${pathname}`,
      data: null
    });
    
  } catch (error) {
    console.error('服务器错误:', error);
    sendJsonResponse(res, 500, {
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 KSG知识图谱模拟服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 单根节点API: POST /champaign/klg/graph`);
  console.log(`📊 多根节点API: POST /champaign/klg/prjGraph`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
