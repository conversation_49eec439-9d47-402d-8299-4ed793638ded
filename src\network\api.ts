export default async function testAPI<T>(
  current: number,
  limit: number,
  // klgCode: String = "K100761793978175149"
  // klgCode: String = "K100761793978171399"
  klgCode: String = "0"
) {
  const res: Promise<Response> = fetch("/api/klg/graph", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      klgCodes: [klgCode],
      current,
      limit,
    }),
  });
  const temp = (await res).json();
  const result: T = await temp;
  return result;
}

export async function multiplyRootsAPI<T>(
  current: number,
  limit: number,
  spuId: string
) {
  const res: Promise<Response> = fetch("/api/champaign/klg/prjGraph", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      spuId,
      current,
      limit,
    }),
  });
  const temp = (await res).json();
  const result: T = await temp;
  return result;
}

/**
 * 本地服务器测试接口-单根节点
 */
export async function localModeApi<T>(
  current: number,
  limit: number,
  klgCode: String = "0"
) {
  const res: Promise<Response> = fetch("/local/champaign/klg/graph", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      klgCodes: [klgCode],
      current,
      limit,
    }),
  });
  const temp = (await res).json();
  const result: T = await temp;
  return result;
}

/**
 * 本地服务器测试接口-多根节点
 */
export async function localMode2Api<T>(
  current: number,
  limit: number,
  klgCode: String = "0"
) {
  const res: Promise<Response> = fetch("/local/champaign/klg/prjGraph", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      klgCodes: [klgCode],
      current,
      limit,
    }),
  });
  const temp = (await res).json();
  const result: T = await temp;
  return result;
}
